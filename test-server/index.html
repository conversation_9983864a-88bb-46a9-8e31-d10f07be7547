
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .log { background: #28a745; color: white; }
        .warn { background: #ffc107; color: black; }
        .error { background: #dc3545; color: white; }
        .info { background: #17a2b8; color: white; }
        .debug { background: #6f42c1; color: white; }
        .auto { background: #6c757d; color: white; }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
        }
        .counter {
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Console Test Page</h1>
        <p>Use this page to test the Trad Console Monitor extension.</p>
        
        <div class="status">
            <div>Auto-logging: <span id="autoStatus">Stopped</span></div>
            <div>Messages sent: <span id="counter" class="counter">0</span></div>
        </div>

        <h3>Manual Console Messages</h3>
        <button class="log" onclick="sendLog()">console.log</button>
        <button class="warn" onclick="sendWarn()">console.warn</button>
        <button class="error" onclick="sendError()">console.error</button>
        <button class="info" onclick="sendInfo()">console.info</button>
        <button class="debug" onclick="sendDebug()">console.debug</button>

        <h3>Auto Console Messages</h3>
        <button class="auto" onclick="toggleAuto()" id="autoBtn">Start Auto-logging</button>
        <p><small>Auto-logging sends a random console message every 2 seconds</small></p>

        <h3>Test Objects and Arrays</h3>
        <button class="log" onclick="sendObject()">Log Object</button>
        <button class="log" onclick="sendArray()">Log Array</button>
        <button class="error" onclick="sendComplexError()">Complex Error</button>
    </div>

    <script>
        let counter = 0;
        let autoInterval = null;
        let isAutoRunning = false;

        function updateCounter() {
            counter++;
            document.getElementById('counter').textContent = counter;
        }

        function sendLog() {
            console.log('Test log message', { timestamp: new Date().toISOString() });
            updateCounter();
        }

        function sendWarn() {
            console.warn('Test warning message', 'Additional warning data');
            updateCounter();
        }

        function sendError() {
            console.error('Test error message', new Error('Sample error'));
            updateCounter();
        }

        function sendInfo() {
            console.info('Test info message', { info: 'Additional information', value: 42 });
            updateCounter();
        }

        function sendDebug() {
            console.debug('Test debug message', 'Debug data:', Math.random());
            updateCounter();
        }

        function sendObject() {
            const testObj = {
                name: 'Test Object',
                nested: {
                    value: 123,
                    array: [1, 2, 3],
                    boolean: true
                },
                timestamp: Date.now()
            };
            console.log('Complex object:', testObj);
            updateCounter();
        }

        function sendArray() {
            const testArray = [
                'string',
                42,
                { key: 'value' },
                [1, 2, 3],
                null,
                undefined,
                true
            ];
            console.log('Complex array:', testArray);
            updateCounter();
        }

        function sendComplexError() {
            try {
                throw new Error('Complex error with stack trace');
            } catch (e) {
                console.error('Caught error:', e, { context: 'error handling', data: { x: 1, y: 2 } });
                updateCounter();
            }
        }

        function getRandomMessage() {
            const types = ['log', 'warn', 'error', 'info', 'debug'];
            const messages = [
                'Auto-generated message',
                'Random data point',
                'Periodic update',
                'Background process',
                'Timer tick'
            ];
            
            const type = types[Math.floor(Math.random() * types.length)];
            const message = messages[Math.floor(Math.random() * messages.length)];
            const data = { 
                random: Math.random(), 
                timestamp: new Date().toISOString(),
                id: Math.floor(Math.random() * 1000)
            };

            console[type](message, data);
            updateCounter();
        }

        function toggleAuto() {
            if (isAutoRunning) {
                clearInterval(autoInterval);
                autoInterval = null;
                isAutoRunning = false;
                document.getElementById('autoBtn').textContent = 'Start Auto-logging';
                document.getElementById('autoStatus').textContent = 'Stopped';
            } else {
                autoInterval = setInterval(getRandomMessage, 2000);
                isAutoRunning = true;
                document.getElementById('autoBtn').textContent = 'Stop Auto-logging';
                document.getElementById('autoStatus').textContent = 'Running';
                // Send first message immediately
                getRandomMessage();
            }
        }

        // Send initial message when page loads
        window.addEventListener('load', () => {
            console.log('Test page loaded successfully!', { 
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
            updateCounter();
        });

        // Send message every 10 seconds to keep testing active
        setInterval(() => {
            if (!isAutoRunning) {
                console.log('Background heartbeat', { time: new Date().toLocaleTimeString() });
                updateCounter();
            }
        }, 10000);
    </script>
</body>
</html>