{"name": "extension", "displayName": "Extension", "version": "0.0.1", "description": "<PERSON><PERSON> Browser MCP", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "prettier": "3.2.4", "typescript": "5.3.3"}, "manifest": {"permissions": ["tabs"], "host_permissions": ["https://*/*"]}}